<script setup lang="ts">

import HeaderBreadcrumb from "@/components/HeaderBreadcrumb.vue";
import {computed, onMounted, reactive, ref} from "vue";
import request from "@/net";
import Swal from "sweetalert2";
import router from "@/router";

function checkOrder() {
    if (order.firstName === '' || order.lastName === '' || order.country === '' || order.city === '' || order.steet1 === '' || order.postal === '' || order.email === '') {
        Swal.fire({
            title: '请填写完整信息',
            icon: 'warning',
            showConfirmButton: false,
            timer: 1500
        })
        return false;
    }
    return true;
}

function createOrder() {
    if (!checkOrder()) return;
    request.post('/system/order/create',
        { ...order,name: order.firstName + ' ' + order.lastName }).then(data => {
        if (data.data.code === 200) {
            Swal.fire({
                title: '订单创建成功!',
                icon: 'success',
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                router.push('/')
            })
        } else {
            Swal.fire({
                title: '订单创建失败!',
                text: data.data.msg,
                icon: 'error',
                showConfirmButton: false,
                timer: 1500
            })
        }
    })
}

const order = reactive({
    firstName: '',
    lastName: '',
    company: '',
    country: '',
    city: '',
    steet1: '',
    steet2: '',
    postal: '',
    phone: '',
    email: '',
    note: ''
})
const data = ref([]);
const total = computed(() => {
  return data.value.map(item => item.count * item.price).reduce((a, b) => a + b, 0) });

onMounted(() =>
    request.get("/system/item/list").then(res => {
      data.value = res.data.rows;
    })
)

</script>

<template>
    <div>
        <header-breadcrumb/>
        <div class="checkout-page-area">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="client-address-area">
                            <div class="row">
                                <div class="col-12">
                                    <div class="checkout-heading">
                                        <h4>账单详情</h4>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="firstName" class="form-label">名字 *</label>
                                    <input type="text" class="form-control" v-model="order.firstName" id="firstName" placeholder="" :required="true">
                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="lastName" class="form-label">姓氏 *</label>
                                    <input type="text" class="form-control" id="lastName" v-model="order.lastName" placeholder="" required>
                                </div>

                                <div class="col-12 mb-4">
                                    <label for="comName" class="form-label">公司名称（选填）</label>
                                    <input type="text" class="form-control" v-model="order.company" id="comName" placeholder="">
                                </div>

                                <div class="col-12 mb-4">
                                    <label for="country" class="form-label">国家/地区 *</label>
                                    <select class="form-select" id="country" v-model="order.country" required>
                                        <option value="">请选择...</option>
                                        <option value="china">中国</option>
                                        <option value="usa">美国</option>
                                    </select>

                                </div>

                                <div class="col-12 mb-4">
                                    <label for="address" class="form-label">详细地址 1 *</label>
                                    <input type="text" class="form-control" v-model="order.steet1" id="address" placeholder="" required>
                                </div>

                                <div class="col-12 mb-4">
                                    <input type="text" class="form-control" id="address1" v-model="order.steet2" placeholder="详细地址 2" required>
                                </div>

                                <div class="col-12 mb-4">
                                    <label for="country2" class="form-label">城市 *</label>
                                    <select class="form-select" id="country2"  v-model="order.city" required>
                                        <option value="">请选择...</option>
                                        <option value="beijing">北京</option>
                                        <option value="shanghai">上海</option>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="zip" class="form-label">邮政编码 *</label>
                                    <input type="text" class="form-control" v-model="order.postal" id="zip" placeholder="" required>

                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="phone" class="form-label">电话号码</label>
                                    <input type="number" class="form-control" v-model="order.phone" id="phone" placeholder="" required>
                                </div>

                                <div class="col-12">
                                    <label for="email" class="form-label">电子邮箱 *</label>
                                    <input type="email" class="form-control" v-model="order.email" id="email" placeholder="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-12">
                                <div class="checkout-heading">
                                    <h4>附加信息</h4>
                                </div>
                            </div>
                            <div class="col-12 mb-5">
                                <label for="lastName" class="form-label">订单备注（选填）</label>
                                <textarea v-model="order.note" class="form-control" placeholder="关于您订单的备注，例如：配送特殊说明" id="message" name="message" cols="30" rows="10"></textarea>
                            </div>

                            <div class="col-12">
                                <div class="checkout-order-area">
                                    <h4>您的订单</h4>
                                    <ul class="order-list-check mb-5" >
                                        <li class="order-list-header">
                                          <span class="heading-title-pro">商品</span>
                                          <span class="heading-title-pro">小计</span>
                                        </li>
                                        <li class="order-list-info-pro" v-for="item in data" :key="item.id">
                                          <span class="product-list-single">
                                          <img class="order-product" src="@/assets/img/bg-img/inner/shop-1.png" alt="">
                                            <div>
                                              <div>{{ item.title }}</div>
                                              <div>x{{ item.count }}</div>
                                            </div>

                                          </span>
                                            <span class="pro-price">¥{{ item.price * item.count }}</span>
                                        </li>
                                        <li class="order-price">
                                          <span>小计</span>
                                          <span class="pur-price">¥{{ total }}</span>
                                        </li>
                                        <li class="order-price">
                                          <span>总计</span>
                                          <span class="pur-price">¥{{ total * 1.08375 }}</span>
                                        </li>
                                    </ul>

                                    <h4 class="mb-3">支付方式</h4>

                                    <div class="payment-gate-sys">
                                        <div class="form-check mb-4">
                                            <input id="credit" name="paymentMethod" type="radio" class="form-check-input" checked="" required>
                                            <label class="form-check-label" for="credit">信用卡</label>
                                        </div>
                                        <div class="form-check mb-4">
                                            <input id="debit" name="paymentMethod" type="radio" class="form-check-input" required>
                                            <label class="form-check-label" for="debit">借记卡</label>
                                        </div>
                                        <div class="form-check mb-4">
                                            <input id="paypal" name="paymentMethod" type="radio" class="form-check-input" required>
                                            <label class="form-check-label" for="paypal">支付宝</label>
                                        </div>
                                    </div>
                                    <div class="pay-policy-des">
                                        <p>您的个人数据将用于处理您的订单、支持您在本网站上的体验以及用于我们隐私政策中描述的其他目的。</p>
                                    </div>
                                    <div class="post-btn-area">
                                        <button type="submit" class="auth-btn post" @click="createOrder">提交订单</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>

</style>