<script setup lang="ts">
import { getToken, removeToken } from "@/utils/token";
import {computed, ref} from "vue";
import request from "@/net";
import {useAccount} from "@/stores/user";
import router from "@/router";
import Swal from "sweetalert2";

const account = useAccount();
const isLogin = computed(() => !!getToken())
const showDropdown = ref(false)

const handleMouseEnter = () => {
    showDropdown.value = true
}

const handleMouseLeave = () => {
    showDropdown.value = false
}

const handleLogout = () => {
    Swal.fire({
        title: '确认退出登录？',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            removeToken()
            // 手动重置用户信息
            account.info.avatar = ''
            account.info.userName = ''
            account.info.nickname = ''
            account.info.email = ''
            account.cart.count = 0
            router.push('/login')
            Swal.fire({
                title: '已退出登录',
                icon: 'success',
                showConfirmButton: false,
                timer: 1500
            })
        }
    })
}
</script>

<template>
  <div class="top-header">
    <!-- Container -->
    <div class="container h-100">
      <div class="row h-100 align-items-center">
        <div class="col-md-6 col-lg-7">
          <div class="contact-info d-flex align-items-center">
            <a href="mailto:<EMAIL>"><span class="icon-mail"></span> <span
                class="text-white contact-desc"><EMAIL></span></a>
            <a href=""><span class="icon-phone-alt-solid-11"></span> <span class="text-white contact-desc">400 - 888 -
                  0001</span></a>
          </div>
        </div>
        <div class="col-md-6 col-lg-5">
          <div class="d-flex align-items-center top-social-conatct">
            <!-- Dropdown -->
            <div class="top-social-icon">
              <ul class="header-icon">
                <li><span class="text-white">关注我们 :</span></li>
                <li><a href="#"><span class="icon-facebook-f"></span></a></li>
                <li><a href="#"><span class="icon-linkedin-in"></span></a></li>
                <li><a href="#"><span class="icon-twitter"></span></a></li>
                <li><a href="#"><span class="icon-instagram"></span></a></li>
              </ul>
            </div>
            <!-- End Dropdown -->
            <div class="contact-info" v-if="isLogin">
              <div class="user-dropdown"
                   @mouseenter="handleMouseEnter"
                   @mouseleave="handleMouseLeave">
                  <img :src="`${request.defaults.baseURL}${account.info.avatar}`"
                       class="user-avatar"
                       role="button">
                  <ul class="dropdown-menu" :class="{ 'show': showDropdown }">
                      <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                      <li><hr class="dropdown-divider"></li>
                      <li><a class="dropdown-item" href="#" @click.prevent="handleLogout">退出登录</a></li>
                  </ul>
              </div>
            </div>
            <div class="contact-info" v-if="!isLogin">
              <a class="login-btn" href="/register"><span class="icon-user-plus-solid-1"></span>注册</a>
            </div>
            <div class="contact-info" v-if="!isLogin">
              <a class="login-btn" href="/login"><span class="icon-user-plus-solid-1"></span>登录</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.1);
}

.user-dropdown {
    position: relative;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 120px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175);
    z-index: 9999;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    list-style: none;
    margin: 0;
}

.dropdown-menu.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.user-dropdown .dropdown-menu .dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 0.5rem 1rem !important;
    color: #333 !important;
    text-decoration: none !important;
    background-color: transparent !important;
    border: 0 !important;
    transition: background-color 0.15s ease-in-out !important;
    font-size: 14px !important;
    font-family: var(--ff-heading) !important;
    white-space: nowrap !important;
}

.user-dropdown .dropdown-menu .dropdown-item:hover,
.user-dropdown .dropdown-menu .dropdown-item:focus {
    background-color: #f8f9fa !important;
    color: #16181b !important;
}

.dropdown-divider {
    height: 0;
    margin: 0.3rem 0;
    overflow: hidden;
    border-top: 1px solid rgba(0,0,0,.15);
}

/* 解决 top-header overflow hidden 的问题 */
:deep(.top-header) {
    overflow: visible !important;
}
</style>