<script setup lang="ts">
import bgImg from '@/assets/img/bg-img/home-3/bg.png'
</script>

<template>
  <div class="breadcrumb-area-2 bg-overlay-3 relative bg-img" :style="{ backgroundImage: `url(${bgImg})` }">
    <div class="container h-100">
      <div class="row h-100 align-items-center">
        <div class="col-12">
          <div class="breadcrumb-conetnt">
            <ul class="bread-list">
              <li><a href="#">首页</a></li>
              <li><i class="icon-down-arrow-11"></i></li>
              <li>结算</li>
            </ul>
            <h4 class="mb-0">结算</h4>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>