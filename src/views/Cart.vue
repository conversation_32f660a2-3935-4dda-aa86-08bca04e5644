<script setup lang="ts">
import bg from "@/assets/img/bg-img/home-3/bg.png";
import request from "@/net";
import {computed, onMounted, ref} from "vue";
import Swal from "sweetalert2";
import router from "@/router";

const data = ref([]);
const total = computed(() => {
  return data.value.map(item => item.count * item.price).reduce((a, b) => a + b, 0) });

function deleteItem(id) {
  request.delete(`/system/item/delete/${id}`).then(res => {
    if (res.data.code === 200) {
      Swal.fire({
        icon: 'success',
        title: '商品删除成功',
        showConfirmButton: false,
        timer: 1500
      })
      const index = data.value.findIndex(item => item.id === id);
      data.value.splice(index, 1);
    }
    else {
      Swal.fire({
        icon: 'error',
        title: '商品删除失败',
        text: res.data.msg,
        showConfirmButton: false,
        timer: 1500
      })
    }
  })
}
function increment(id) {
  request.post(`/system/item/increase/${id}`).then(res => {
    if (res.data.code === 200) {
      Swal.fire({
        icon: 'success',
        title: '商品数量增加成功',
        showConfirmButton: false,
        timer: 1500
      })
      const item = data.value.find(item => item.id === id);
      item.count++;
    }
    else {
      Swal.fire({
        icon: 'error',
        title: '商品数量增加失败',
        text: res.data.msg,
        showConfirmButton: false,
        timer: 1500
      })
    }
  })
}
function decrement(id) {
  request.post(`/system/item/decrease/${id}`).then(res => {
    if (res.data.code === 200) {
      Swal.fire({
        icon: 'success',
        title: '商品数量减少成功',
        showConfirmButton: false,
        timer: 1500
      })
      const item = data.value.find(item => item.id === id);
      if (--item.count <= 0){
        const index = data.value.findIndex(item => item.id === id);
        data.value.splice(index, 1);
      }else {
        item.count--;
      }
    }
    else {
      Swal.fire({
        icon: 'error',
        title: '商品数量减少失败',
        text: res.data.msg,
        showConfirmButton: false,
        timer: 1500
      })
    }
  })
}

onMounted(() =>
    request.get("/system/item/list").then(res => {
      data.value = res.data.rows;
    })
)
</script>

<template>
  <div>
    <div id="smooth-content">
      <div class="breadcrumb-area-2 bg-overlay-3 relative bg-img" :style="{ backgroundImage: `url(${bg})`}">
        <div class="container h-100">
        <div class="row h-100 align-items-center">
                <div class="col-12">
              <div class="breadcrumb-conetnt">
                <ul class="bread-list">
                  <li><a href="#">首页</a></li>
                  <li><i class="icon-down-arrow-11"></i></li>
                  <li>购物车</li>
                </ul>
                <h4 class="mb-0">购物车</h4>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="product-cart-area">
        <div class="container">
          <div class="row">
            <div class="col-12">
              <div class="cart-table">
                <div class="table-responsive">
                  <table class="table">
                    <thead class="table-navy">
                    <tr><th></th>
                      <th></th>
                      <th>产品</th>
                      <th>价格</th>
                      <th>数量</th>
                      <th>小计</th>
                    </tr></thead>
                    <tbody>
                    <tr v-for="item in data" :key="item.id">
                      <td><span @click="deleteItem(item.id)" style="cursor: pointer;" class="icon-icon_close_alt2"></span></td>
                      <td class="cart-pro-img"><img src="@/assets/img/bg-img/inner/shop-1.png" alt=""></td>
                      <td>{{ item.title }}</td>
                      <td>${{ item.price }}</td>
                      <td>
                        <div class="qty-pro-area">
                          <form class="cart-form">
                            <div class="order-plus-minus d-flex align-items-center">
                              <div class="quantity-button-handler" @click="decrement(item.id)">-</div>
                              <input class="form-control cart-quantity-input" type="text" name="quantity" :value="item.count">
                              <div class="quantity-button-handler" @click="increment(item.id)">+</div>
                            </div>
                            <div class="fav-icon">
                              <i class="icon-icon_ribbon_alt"></i>
                            </div>
                          </form>
                        </div>
                      </td>
                      <td>${{ item.count * item.price }}</td>
                    </tr>
                    </tbody>
                  </table>
                </div>


              </div>
            </div>
          </div>

          <div class="row justify-content-end">
            <div class="col-lg-5 col-xl-4">
              <div class="cart-total-card">
                <h4>购物车总计</h4>

                <div class="cart-list">
                  <p>小计</p>
                  <p>${{ total }}</p>
                </div>

                <div class="cart-list">
                  <div class="cart-tax-pri">
                    <p>税</p>
                    <p>美国 (8.375%)</p>
                  </div>
                  <p>${{ (total * 0.08375).toFixed(2) }}</p>
                </div>

                <div class="total-order">
                  <h5>订单总计</h5>
                  <div class="total-order-desc">
                    <h6>${{ total * 1.08375 }}</h6>
                    <span>不含税: ${{ total }}</span>
                  </div>
                </div>

                <button type="submit" class="pro-btn mt-4" @click="router.push('/order')"><span class="icon-padlock"></span> 前往结账</button>

              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <footer class="footer-wrap bg--dark">
        <!-- Shape -->
        <div class="f-shape-1 wow fadeInUp" data-wow-delay="900ms" style="visibility: visible; animation-delay: 900ms; animation-name: fadeInUp;">
          <img src="@/assets/img/bg-img/f-shap-1.png" alt="">
        </div>
        <!-- Shape -->
        <div class="f-shape-2 wow fadeInRight" data-wow-delay="700ms" style="visibility: visible; animation-delay: 700ms; animation-name: fadeInRight;">
          <img src="@/assets/img/bg-img/f-shap-2.png" alt="">
        </div>

      </footer>
    </div>
  </div>
</template>

<style scoped>

</style>